import { HydrationBoundary, dehydrate } from "@tanstack/react-query"
import type { SearchParams } from "nuqs/server"

import { DEFAULT_LIMIT } from "@/constants"
import { getQueryClient, trpc } from "@/trpc/server"

import { loadProductFilters } from "@/modules/products/search-params"
import { ProductListView } from "@/modules/products/ui/views/product-list-view"

interface Props {
  searchParams: Promise<SearchParams>
}

const Page = async ({ searchParams }: Props) => {
  const filters = await loadProductFilters(searchParams)

  const queryClient = getQueryClient()
  void queryClient.prefetchInfiniteQuery(
    trpc.products.getMany.infiniteQueryOptions({
      ...filters,
      limit: DEFAULT_LIMIT
    })
  )

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ProductListView />
    </HydrationBoundary>
  )
}

export default Page
