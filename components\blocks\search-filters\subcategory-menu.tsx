import type { Category } from "@/payload-types";
import Link from "next/link";


interface Props {
    category: Category;
    isOpen: boolean;
    position: { top: number; left: number };
}

export const SubcategoryMenu = ({
    category,
    isOpen,
    position
}: Props) => {
    if (!isOpen || !category.subcategories?.docs || category.subcategories.docs.length === 0) {
        return null;
    }


    return (
        <div
            className="fixed z-100"
            style={{
                top: position.top,
                left: position.left,
            }}
        >
            {/* Invisible bridge to maintain hover */}
            <div className="h-3 w-60" />
            <div /* style={{ backgroundColor }} */ className="w-60 text-black rounded-md overflow-hidden border shadow-[4px_4px_0px_0px rgba(0,0,0,0.1)] -translate-x-[2px] -translate-y-[2px]">
                {category.subcategories.docs?.map((subcategory) => {
                    // Handle both string and Category types
                    const cat = typeof subcategory === 'string' ? null : subcategory;
                    if (!cat) return null;
                    return (
                        <Link key={cat.slug} href="/"
                            className="w-full text-left p-4 hover:bg-block hover:text-white flex justify-between items-center underline font-medium">
                            {cat.name}
                        </Link>
                    );
                })}
            </div>
        </div>
    );
}
