import { Routes } from "@/config/constants"
import { siteConfig } from "@/config/site"
import type { MetadataRoute } from "next"

export default function manifest(): MetadataRoute.Manifest {
  // Get key routes for shortcuts (excluding Home)
  const keyRoutes = Routes.filter((route) =>
    ["/blog", "/about", "/contact"].includes(route.href)
  ).slice(0, 3) // Limit to 3 shortcuts for better UX

  return {
    name: siteConfig.name,
    short_name: siteConfig.name,
    description: siteConfig.description,
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#000000",
    orientation: "portrait-primary",
    scope: "/",
    lang: "en-US",
    dir: "ltr",
    categories: ["business", "technology", "design"],
    icons: [
      {
        src: "/favicon.ico",
        sizes: "48x48",
        type: "image/x-icon"
      },
      {
        src: "/images/icon.svg",
        sizes: "any",
        type: "image/svg+xml",
        purpose: "any"
      },
      {
        src: "/images/logo.svg",
        sizes: "any",
        type: "image/svg+xml",
        purpose: "maskable"
      }
    ],
    shortcuts: keyRoutes.map((route) => ({
      name: route.label,
      short_name: route.label,
      description: `Visit our ${route.label.toLowerCase()} page`,
      url: route.href,
      icons: [
        { src: "/images/logo.svg", sizes: "96x96", type: "image/svg+xml" }
      ]
    }))
  }
}
