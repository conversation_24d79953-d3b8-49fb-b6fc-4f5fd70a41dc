"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "@/components/ui/sheet"
import type { CategoriesGetManyOutput } from "@/modules/categories/types"
import { useTRPC } from "@/trpc/client"
import { useQuery } from "@tanstack/react-query"
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export const CategoriesSidebar = ({ open, onOpenChange }: Props) => {
  const trpc = useTRPC()
  const { data } = useQuery(trpc.categories.getMany.queryOptions())

  const router = useRouter()

  const [parentCategories, setParentCategories] =
    useState<CategoriesGetManyOutput | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<
    CategoriesGetManyOutput[1] | null
  >(null)

  //if we have parent categories, show those, otherwise show root categories
  const currentCategories = parentCategories ?? data ?? []

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open)
    setSelectedCategory(null)
    setParentCategories(null)
  }

  const handleCategoryClick = (category: CategoriesGetManyOutput[1]) => {
    if (
      category.subcategories &&
      Array.isArray(category.subcategories.docs) &&
      category.subcategories.docs.length > 0
    ) {
      setParentCategories(
        category.subcategories.docs as CategoriesGetManyOutput
      )
      setSelectedCategory(category)
    } else {
      //this is a leaf category no subcategories
      if (parentCategories && selectedCategory) {
        //this is a subcategories - navigate to category/subcategory
        router.push(`/${selectedCategory.slug}/${category.slug}`)
      } else {
        //this is a root category - navigate to category
        if (category.slug === "all") {
          router.push("/")
        } else {
          router.push(`/${category.slug}`)
        }
      }
      handleOpenChange(false)
    }
  }

  const handleBackClick = () => {
    if (parentCategories) {
      setParentCategories(null)
      setSelectedCategory(null)
    }
  }

  const backgroundColor = selectedCategory?.color || "white"
  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetContent
        side="left"
        className="p-0 transition-none"
        style={{ backgroundColor }}
      >
        <SheetHeader className="p-4 border-b">
          <SheetTitle>Categories</SheetTitle>
        </SheetHeader>
        <ScrollArea className="flex flex-col overflow-y-auto h-full pb-2">
          {parentCategories && (
            <button
              type="button"
              onClick={handleBackClick}
              className="w-full text-left p-4 hover:bg-black hover:text-white flex items-center text-base font-medium"
            >
              <ChevronLeftIcon className="size-4 mr-2" />
              back
            </button>
          )}
          {currentCategories.map((category) => (
            <button
              key={category.slug}
              type="button"
              onClick={() => handleCategoryClick(category)}
              className="w-full text-left p-4 hover:bg-black hover:text-white flex justify-between items-center text-base font-medium cursor-pointer"
            >
              {category.name}
              {category.subcategories &&
                Array.isArray(category.subcategories.docs) &&
                category.subcategories.docs.length > 0 && (
                  <ChevronRightIcon className="size-4" />
                )}
            </button>
          ))}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  )
}
