import { Suspense } from "react"

import { Navbar, NavbarSkeleton } from "@/modules/tenants/ui/components/navbar"
import { getQueryClient, trpc } from "@/trpc/server"
import { HydrationBoundary, dehydrate } from "@tanstack/react-query"

interface LayoutProps {
  children: React.ReactNode
  params: { slug: string }
}

const Layout = async ({ children, params }: LayoutProps) => {
  const { slug } = params;

  const queryClient = getQueryClient()
  void queryClient.prefetchQuery(
    trpc.tenants.getOne.queryOptions({
      slug
    })
  )

  return (
    <div className="min-h-screen bg-[#F4F4F0] flex flex-col">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Suspense fallback={<NavbarSkeleton />}>
          <Navbar slug={slug} />
        </Suspense>
      </HydrationBoundary>
      <div className="flex-1">
        <div className="max-w-(--breakpoint-xl) mx-auto">{children}</div>
      </div>
    </div>
  )
}

export default Layout;
