import type { RefObject } from "react"

export const useDropdownPosition = (
  ref: RefObject<HTMLDivElement | null> | RefObject<HTMLDivElement>
) => {
  const getDropdownPosition = () => {
    if (!ref.current) {
      return { top: 0, left: 0 }
    }

    const rect = ref.current.getBoundingClientRect()
    const dropdownWidth = 240 //width of dropdown (w-60 = 15rem = 240px)

    // Calculate the initial position
    let left = rect.left + window.scrollX
    const top = rect.bottom + window.scrollY

    //check if dropdown would go off right edg of the  viewport
    if (left + dropdownWidth > window.innerWidth) {
      // align to right edge of button instead
      left = rect.right + window.scrollX - dropdownWidth

      //if still off-screen align to the right edge of viewport with some padding
      if (left < 0) {
        left = window.innerWidth - dropdownWidth - 16
      }
    }
    // Ensure  dropdown does not go off the left edge of the viewport
    if (left < 0) {
      left = 16 // Add padding to the left edge
    }
    return { top, left }
  }
  return { getDropdownPosition }
}
