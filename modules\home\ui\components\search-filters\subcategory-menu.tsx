import type { CategoriesGetManyOutput } from "@/modules/categories/types";
import Link from "next/link";


interface Props {
    category: CategoriesGetManyOutput[1]
    isOpen: boolean
}

export const SubcategoryMenu = ({
    category,
    isOpen,
}: Props) => {
    if (!isOpen || !category.subcategories || category.subcategories.length === 0) {
        return null;
    }

    const backgroundColor = category.color || "#F5F5F5";


    return (
        <div
            className="absolute z-100"
            style={{
                top: "100%",
                left: 0,
            }}
        >
            {/* Invisible bridge to maintain hover */}
            <div className="h-3 w-60" />
            <div style={{ backgroundColor }} className="w-60 text-black rounded-md overflow-hidden border shadow-[4px_4px_0px_0px rgba(0,0,0,0.1)] -translate-x-[2px] -translate-y-[2px]">
                {category.subcategories.docs?.map((subcategory) => {
                    // Handle both string and Category types
                    const cat = typeof subcategory === 'string' ? null : subcategory;
                    if (!cat) return null;
                    return (
                        <Link key={cat.slug} href={`/${category.slug}/${cat.slug}`}
                            className="w-full text-left p-4 hover:bg-block hover:text-white flex justify-between items-center underline font-medium">
                            {cat.name}
                        </Link>
                    );
                })}
            </div>
        </div>
    );
}