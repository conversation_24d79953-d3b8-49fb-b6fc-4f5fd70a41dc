import type { Category } from "@/payload-types"

import { baseProcedure, createTR<PERSON>Router } from "@/trpc/init"

export const categoriesRouter = createTRPCRouter({
  getMany: baseProcedure.query(async ({ ctx }) => {
    const data = await ctx.db.find({
      collection: "categories",
      depth: 1, // Populate subcategories , subcategories 0 will be a type of category
      pagination: false, // Disable pagination to fetch all categories
      where: {
        parent: {
          exists: false // This will find all categories that do not have a parent
        }
      },
      sort: "name"
    })
    const formattedData = data.docs.map((doc) => ({
      ...doc,
      Subcategories: (doc.subcategories?.docs ?? []).map((doc) => ({
        ...(doc as Category)
      }))
    }))
    return formattedData
  })
})
