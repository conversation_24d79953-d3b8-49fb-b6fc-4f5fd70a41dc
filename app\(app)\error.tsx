"use client"

import { Section } from "@/components/layout"
import { Button } from "@/components/ui/button"
import { env } from "@/config/env"
import { AlertTriangle, RefreshCw, X } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useRef, useState } from "react"

export default function ErrorPage({
  error,
  reset
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()
  const [showFullError, setShowFullError] = useState(false)
  const [dismissed, setDismissed] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Log error to monitoring service
    console.error("Application error:", error)

    // In production, send to error tracking service
    if (env.NODE_ENV === "production") {
      // Example: Sentry.captureException(error)
    }

    // Auto-show full error after 3 seconds if user doesn't interact
    const timer = setTimeout(() => {
      if (!dismissed) {
        setShowFullError(true)
      }
    }, 30000)

    return () => clearTimeout(timer)
  }, [error, dismissed])

  // If dismissed, try to preserve any existing content
  if (dismissed) {
    return (
      <div
        ref={contentRef}
        suppressHydrationWarning
        dangerouslySetInnerHTML={{
          __html: contentRef.current?.innerHTML || ""
        }}
      />
    )
  }

  // Show subtle error banner first
  if (!showFullError) {
    return (
      <>
        <div ref={contentRef} suppressHydrationWarning>
          {/* Preserve any existing content */}
        </div>
        <div className="fixed bottom-0 left-0 right-0 bg-destructive/75 text-destructive-foreground py-4 px-6 shadow-lg border-t z-50">
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            <div className="flex items-center justify-center gap-3 relative">
              <AlertTriangle className="size-12 text-background" />
              <div className="flex flex-col">
                <span className="font-semibold text-background text-xl">
                  Something went wrong
                </span>
                <p className="text-sm opacity-90 !mt-0">
                  An error occurred while rendering this page
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => reset()}
                className="flex flex-1 items-center gap-1 bg-transparent text-background hover:bg-background border-background border-2 hover:text-foreground"
              >
                <RefreshCw className="size-4" />
                Retry
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={() => setShowFullError(true)}
                className="flex flex-1 items-center gap-1 bg-transparent text-background hover:bg-background border-background border-2 hover:text-foreground"
              >
                Details
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setDismissed(true)}
                className="bg-transparent text-background hover:bg-background border-background border-2 hover:text-foreground"
              >
                <X className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Show full error page
  return (
    <main>
      <Section
        container
        className="min-h-screen flex items-center justify-center"
      >
        <div className="text-center space-y-6 max-w-md">
          <AlertTriangle className="mx-auto h-16 w-16 text-destructive" />
          <div className="space-y-2">
            <h1 className="text-2xl font-bold">Something went wrong!</h1>
            <p className="text-muted-foreground">
              We apologize for the inconvenience. An unexpected error has
              occurred.
            </p>
            {env.NODE_ENV === "development" && (
              <details className="mt-4 p-4 bg-muted rounded-lg text-left">
                <summary className="cursor-pointer font-medium">
                  Error Details
                </summary>
                <pre className="mt-2 text-sm overflow-auto">
                  {error.message}
                  {error.stack && `\n\n${error.stack}`}
                </pre>
              </details>
            )}
          </div>
          <div className="flex gap-4 justify-center">
            <Button onClick={() => reset()} className="flex items-center gap-2">
              <RefreshCw className="size-4" />
              Try again
            </Button>
            <Button variant="outline" onClick={() => router.push("/")}>
              Go home
            </Button>
            <Button
              variant="ghost"
              onClick={() => setShowFullError(false)}
              className="text-muted-foreground"
            >
              Back to banner
            </Button>
          </div>
        </div>
      </Section>
    </main>
  )
}
