import { ImageResponse } from "next/og"

// Image metadata
export const size = {
  width: 180,
  height: 180
}

export const contentType = "image/png"

// Image generation
export default function AppleIcon() {
  return new ImageResponse(
    <div
      style={{
        backgroundColor: "white",
        width: "100%",
        height: "100%",
        borderRadius: "22.37%", // Apple's rounded corner ratio
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      }}
    >
      <svg
        width="160"
        height="160"
        viewBox="0 0 256 256"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M61.5756 116.379C64.5071 122.172 68.5563 127.071 73.6691 131.387C82.3739 138.734 94.0775 144.286 107.899 149.981L116.168 153.277L116.831 160.369L119.747 203.756C103.226 161.574 60.3688 128 0 128C28.3173 128 42.3028 124.211 61.5756 116.379Z"
          fill="black"
        />
        <path
          d="M136.262 52.27C152.788 94.4342 195.643 128 256 128C227.28 128 213.025 131.897 193.605 139.957C190.798 135.098 187.118 130.705 182.647 126.672C173.936 118.815 162.244 112.342 148.33 106.252L139.832 102.723L139.175 95.6397L136.262 52.27Z"
          fill="black"
        />
        <path
          d="M128.002 256C128.002 256 128.002 227.555 156.445 213.333C170.667 206.222 191.998 191.326 191.998 163.556C191.998 135.111 170.666 120.889 135.113 106.667L128.002 0C128.002 0 128.002 28.4444 99.5598 42.6667C85.3383 49.7778 63.5811 64.6771 64.0061 92.4444C64.5179 125.881 85.3381 135.111 120.892 149.333L128.002 256Z"
          fill="url(#stellar_gradient)"
        />
        <path
          d="M170.362 182.953C171.088 186.514 171.192 189.797 171.137 192.238C171.028 194.422 170.447 197.188 169.442 199.111C175.001 193.32 177.641 187.055 177.772 178.867C177.724 173.418 175.473 168.413 171.781 164.56C164.293 157.739 155.178 156.374 145.626 155.079C141.355 154.498 137.122 153.809 132.952 152.859C132.396 152.732 120.887 149.333 120.887 149.333C120.887 149.333 166.419 163.623 170.362 182.953Z"
          fill="black"
          fillOpacity="0.2"
        />
        <path
          d="M85.6331 73.0472C84.9067 69.4864 84.8026 66.2034 84.8584 63.7616C84.9668 61.5781 85.5479 58.8122 86.553 56.8889C80.9938 62.6803 78.3544 68.9453 78.2228 77.1332C78.2707 82.5816 80.5216 87.5867 84.2143 91.44C91.7017 98.2611 100.817 99.6265 110.369 100.921C114.64 101.502 118.872 102.191 123.043 103.141C123.599 103.268 135.108 106.667 135.108 106.667C135.108 106.667 89.5761 92.377 85.6331 73.0472Z"
          fill="black"
          fillOpacity="0.2"
        />
        <defs>
          <linearGradient
            id="stellar_gradient"
            x1="127.282"
            y1="0.407422"
            x2="118.964"
            y2="255.981"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.25" stopColor="#A64FFA" />
            <stop offset="0.75" stopColor="#74FFFA" />
          </linearGradient>
        </defs>
      </svg>
    </div>,
    // ImageResponse options
    {
      // For convenience, we can re-use the exported icons size metadata
      // config to also set the ImageResponse's width and height.
      ...size
    }
  )
}
