{"name": "multitenant-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate:types": "payload generate:types", "db:fresh": "payload migrate:fresh", "db:seed": "bun run src/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@payloadcms/db-mongodb": "3.33.0", "@payloadcms/next": "3.33.0", "@payloadcms/payload-cloud": "3.33.0", "@payloadcms/plugin-multi-tenant": "3.33.0", "@payloadcms/richtext-lexical": "3.33.0", "@payloadcms/storage-vercel-blob": "3.33.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "5.72.1", "@trpc/client": "11.0.3", "@trpc/server": "11.0.3", "@trpc/tanstack-react-query": "11.0.3", "class-variance-authority": "^0.7.1", "client-only": "0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "graphql": "^16.8.1", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "nuqs": "^2.4.1", "payload": "^3.33.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "server-only": "0.0.1", "sonner": "^2.0.3", "stripe": "^18.0.0", "superjson": "^2.2.2", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.4", "@eslint/eslintrc": "^3"}}