"use client"

import { useQuery } from "@tanstack/react-query"
import { MenuIcon } from "lucide-react"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useTRPC } from "@/trpc/client"

import { NavbarSidebar } from "@/components/layout/navbar-sidebar"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["700"]
})

interface NavbarItemProps {
  href: string
  children: React.ReactNode
  isActive?: boolean
}

const NavbarItem = ({ href, children, isActive }: NavbarItemProps) => {
  return (
    <Button
      variant="outline"
      className={cn(
        "bg-transparent hover:bg-transparent rounded-full hover:border-primary border-transparent px-3.5 text-lg",
        isActive && "bg-black text-white hover:bg-black hover:text-red-500"
      )}
    >
      <Link href={href}>{children}</Link>
    </Button>
  )
}
const navbarItems = [
  {
    href: "/",
    children: "Home"
  },
  {
    href: "/about",
    children: "About"
  },
  {
    href: "/features",
    children: "Features"
  },
  {
    href: "/pricing",
    children: "Pricing"
  },
  {
    href: "/contact",
    children: "Contact"
  }
]

const Header = () => {
  const pathname = usePathname()
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  const trpc = useTRPC()
  const session = useQuery(trpc.auth.session.queryOptions())

  return (
    <nav className="h-20 flex border-b justify-between font-medium bg-white">
      <Link href="/" className="pl-6 flex items-center">
        <span className={cn(poppins.className, "text-5xl font-semibold")}>
          funroad
        </span>
      </Link>
      <NavbarSidebar
        items={navbarItems}
        open={isSidebarOpen}
        onOpenChange={setIsSidebarOpen}
      />

      <div className="items-center gap-4 hidden lg:flex">
        {navbarItems.map((item) => (
          <NavbarItem
            key={item.href}
            href={item.href}
            isActive={pathname === item.href}
          >
            {item.children}
          </NavbarItem>
        ))}
      </div>

      {session.data?.user ? (
        <div className="hidden lg:flex">
          <Button
            asChild
            className="border-l border-b-0 border-r-0 border-t-0 px-12 h-full rounded-none bg-black text-white hover:bg-pink-500 hover:text-black transition-colors text-lg"
          >
            <Link href="/admin">Dashboard</Link>
          </Button>
        </div>
      ) : (
        <div className="hidden lg:flex">
          <Button
            asChild
            variant="secondary"
            className="border-l border-b-0 border-r-0 border-t-0 px-12 h-full rounded-none bg-white hover:bg-pink-500 transition-colors text-lg"
          >
            <Link prefetch href="/sign-in">
              Login
            </Link>
          </Button>
          <Button
            asChild
            className="border-l border-b-0 border-r-0 border-t-0 px-12 h-full rounded-none bg-black text-white hover:bg-pink-500 hover:text-black transition-colors text-lg"
          >
            <Link prefetch href="/sign-up">
              Start selling
            </Link>
          </Button>
        </div>
      )}

      <div className="lg:hidden flex items-center justify-center">
        <Button
          variant="ghost"
          className="size-12 border-transparent bg-white"
          onClick={() => setIsSidebarOpen(true)}
        >
          <MenuIcon className="h-6 w-6" />
        </Button>
      </div>
    </nav>
  )
}
export { Header }

/* "use client"

import { Link } from "@/components/layout/link"
import { Routes } from "@/config/constants"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

const Header = () => {
  const pathname = usePathname()
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 0)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header className={cn("glass-morph", scrolled && "shadow-md")}>
      <div data-container>
        <Link
          href="/"
          className="relative size-12 flex items-center hover:text-foreground hover:no-underline"
        >
          <Image
            src="/images/logo.svg"
            alt="Logo"
            fill
            className="aspect-square object-contain center font-main rounded-full bg-background"
          />
        </Link>
        <nav className="flex flex-row gap-8">
          {Routes.map((route) => (
            <Link
              key={route.label}
              href={route.href}
              className={cn(
                "text-foreground/50 text-sm hover:text-foreground",
                pathname === route.href && "text-foreground"
              )}
            >
              {route.label}
            </Link>
          ))}
        </nav>
      </div>
    </header>
  )
}

export { Header }
 */
