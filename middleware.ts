import { NextRequest, NextResponse } from "next/server";

export const config = {
  matcher: [
     /*
     * Match all paths except for:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. all root files inside /public (e.g. /favicon.ico)
     */
    "/((?!api/|_next/|_static/|_vercel|media/|[\\w-]+\\.\\w+).*)",
  ],
};

export default async function middleware(req: NextRequest) {
  const url = req.nextUrl;
  // Extract the hostname (e.g., "antonio.funroad.com" or "john.localhost:3000")
  const hostname = req.headers.get("host") || "";

  const rootDomain = process.env.NEXT_PUBLIC_ROOT_DOMAIN || "";

  if (hostname.endsWith(`.${rootDomain}`)) {
    const tenantSlug = hostname.replace(`.${rootDomain}`, "");
    return NextResponse.rewrite(new URL(`/tenants/${tenantSlug}${url.pathname}`, req.url));
  }

  return NextResponse.next();
};









   






// import { NextResponse, NextRequest } from "next/server"

// export function middleware(request: NextRequest) {
//   const { pathname } = request.nextUrl

//   // Handle temporary route rewrite
//   if (pathname.startsWith("/temp")) {
//     return NextResponse.rewrite(new URL("/", request.url))
//   }

//   // Generate nonce for CSP
//   const nonce = Buffer.from(crypto.randomUUID()).toString("base64")

//   const cspHeader = `
//     default-src 'self';
//     script-src 'self' 'nonce-${nonce}' 'strict-dynamic';
//     style-src 'self' 'nonce-${nonce}' 'unsafe-inline';
//     img-src 'self' blob: data:;
//     font-src 'self';
//     object-src 'none';
//     base-uri 'self';
//     form-action 'self';
//     frame-ancestors 'none';
//     upgrade-insecure-requests;
//   `

//   // Replace newline characters and spaces
//   const contentSecurityPolicyHeaderValue = cspHeader
//     .replace(/\s{2,}/g, " ")
//     .trim()

//   const requestHeaders = new Headers(request.headers)

//   requestHeaders.set("x-nonce", nonce)
//   requestHeaders.set(
//     "Content-Security-Policy",
//     contentSecurityPolicyHeaderValue
//   )

//   const response = NextResponse.next({
//     request: {
//       headers: requestHeaders
//     }
//   })

//   return response
// }

// export const config = {
//   matcher: [
//     /*
//      * Match all request paths except for the ones starting with:
//      * - api (API routes)
//      * - _next/static (static files)
//      * - _next/image (image optimization files)
//      * - favicon.ico, sitemap.xml, robots.txt (metadata files)
//      */
//     {
//       source:
//         "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
//       missing: [
//         { type: "header", key: "next-router-prefetch" },
//         { type: "header", key: "purpose", value: "prefetch" }
//       ]
//     }
//   ]
// }
