import { Routes } from "@/config/constants"
import { siteConfig } from "@/config/site"
import { getBlogs } from "@/utils/server"
import type { MetadataRoute } from "next"

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Get all blog posts
  const blogs = await getBlogs()

  // Generate blog routes from actual blog posts
  const blogRoutes = blogs.map((post) => ({
    url: `${siteConfig.url}/blog/${post.slug}`,
    lastModified: new Date(post.frontmatter.date || new Date()),
    changeFrequency: "weekly" as const,
    priority: 0.7
  }))

  // Generate static routes from constants
  const staticRoutes = Routes.map((route) => ({
    url: `${siteConfig.url}${route.href}`,
    lastModified: new Date().toISOString().split("T")[0],
    changeFrequency:
      route.href === "/blog" ? ("daily" as const) : ("monthly" as const),
    priority: route.href === "/" ? 1.0 : route.href === "/blog" ? 0.9 : 0.8
  }))

  // Combine all routes
  const allRoutes = [...staticRoutes, ...blogRoutes]

  // For large sites, you might want to split into multiple sitemaps
  // Google's limit is 50,000 URLs per sitemap
  if (allRoutes.length > 45000) {
    console.warn(
      `Sitemap has ${allRoutes.length} URLs. Consider implementing generateSitemaps() for better performance.`
    )
  }

  return allRoutes
}
