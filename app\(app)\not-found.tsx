// import { redirect } from "next/navigation"

import { Section } from "@/components/layout"
import { <PERSON> } from "@/components/layout/link"
import { But<PERSON> } from "@/components/ui/button"

export default function NotFoundPage() {
  // redirect("/")

  return (
    <main>
      <Section container>
        <h2>Page not found</h2>
        <p>
          The page you are looking for does not exist. You can go back to the
          home page or check the links below.
        </p>
        <Button asChild>
          <Link href={"/"} className="!text-background mt-8">
            Home
          </Link>
        </Button>
      </Section>
    </main>
  )
}
